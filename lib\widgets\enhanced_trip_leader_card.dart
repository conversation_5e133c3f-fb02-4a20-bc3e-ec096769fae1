import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
// import 'package:share_plus/share_plus.dart'; // Temporarily disabled
import '../constants/app_theme.dart';
import '../models/trip_model.dart';
import '../providers/trip_provider.dart';
import '../utils/navigation_utils.dart';
import '../pages/trip_leader/edit_trip_page.dart';

class EnhancedTripLeaderCard extends StatefulWidget {
  final TripModel trip;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;

  const EnhancedTripLeaderCard({
    super.key,
    required this.trip,
    this.onEdit,
    this.onDelete,
    this.onShare,
  });

  @override
  State<EnhancedTripLeaderCard> createState() => _EnhancedTripLeaderCardState();
}

class _EnhancedTripLeaderCardState extends State<EnhancedTripLeaderCard>
    with TickerProviderStateMixin {
  late AnimationController _rippleController;
  late AnimationController _hoverController;
  late Animation<double> _rippleAnimation;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();

    // Set Arabic locale for timeago
    timeago.setLocaleMessages('ar', timeago.ArMessages());

    // Initialize animations
    _rippleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rippleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rippleController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    // Start ripple animation for active trips
    if (widget.trip.status == 'active' || widget.trip.status == 'published') {
      _rippleController.repeat();
    }
  }

  @override
  void dispose() {
    _rippleController.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTapDown: (_) => _hoverController.forward(),
        onTapUp: (_) => _hoverController.reverse(),
        onTapCancel: () => _hoverController.reverse(),
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow,
                      blurRadius: _isHovered ? 12 : 8,
                      offset: Offset(0, _isHovered ? 6 : 4),
                      spreadRadius: _isHovered ? 2 : 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(),
                      _buildContent(),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Route
                Row(
                  children: [
                    Icon(Icons.location_on, color: AppColors.primary, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${widget.trip.fromCity} ← ${widget.trip.toCity}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // Time posted with better formatting
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 12,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'تم النشر ${timeago.format(widget.trip.createdAt, locale: 'ar')}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Status indicator
          _buildStatusIndicator(),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final isActive =
        widget.trip.status == 'active' || widget.trip.status == 'published';
    final isCancelled = widget.trip.status == 'cancelled';

    if (isActive) {
      return AnimatedBuilder(
        animation: _rippleAnimation,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              // Ripple effect
              Container(
                width: 24 + (_rippleAnimation.value * 12),
                height: 24 + (_rippleAnimation.value * 12),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.success.withOpacity(
                    0.3 * (1 - _rippleAnimation.value),
                  ),
                ),
              ),
              // Core circle
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.success,
                ),
              ),
            ],
          );
        },
      );
    } else if (isCancelled) {
      return Container(
        width: 12,
        height: 12,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.error,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // Trip details row
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  Icons.calendar_today,
                  '${widget.trip.departureDate.day}/${widget.trip.departureDate.month}',
                  AppColors.primary,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  Icons.access_time,
                  widget.trip.departureTime,
                  AppColors.secondary,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  Icons.airline_seat_recline_normal,
                  '${widget.trip.availableSeats}/${widget.trip.totalSeats}',
                  AppColors.accent,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Price and car info
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 12,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${widget.trip.price.toStringAsFixed(0)} درهم',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              if (widget.trip.carModel != null ||
                  widget.trip.carPlate != null) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.trip.carModel != null)
                        Text(
                          widget.trip.carModel!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      if (widget.trip.carPlate != null)
                        Text(
                          widget.trip.carPlate!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.edit,
            label: 'تعديل',
            color: AppColors.primary,
            onTap: () => _handleEdit(),
          ),
          _buildActionButton(
            icon: Icons.delete,
            label: 'حذف',
            color: AppColors.error,
            onTap: () => _handleDelete(),
          ),
          _buildActionButton(
            icon: Icons.share,
            label: 'مشاركة',
            color: AppColors.secondary,
            onTap: () => _handleShare(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleEdit() {
    if (widget.onEdit != null) {
      widget.onEdit!();
    } else {
      // Navigate to EditTripPage with pre-filled data
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EditTripPage(tripId: widget.trip.id),
        ),
      );
    }
  }

  void _handleDelete() {
    if (widget.onDelete != null) {
      widget.onDelete!();
    } else {
      _showDeleteConfirmation();
    }
  }

  void _handleShare() {
    if (widget.onShare != null) {
      widget.onShare!();
    } else {
      _shareTrip();
    }
  }

  void _showDeleteConfirmation() async {
    final confirmed = await NavigationUtils.showConfirmationDialog(
      context,
      title: 'حذف الرحلة',
      content:
          'هل أنت متأكد من حذف هذه الرحلة؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      confirmColor: AppColors.error,
    );

    if (confirmed == true && mounted) {
      final tripProvider = Provider.of<TripProvider>(context, listen: false);
      final success = await tripProvider.deleteTrip(widget.trip.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الرحلة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }

  void _shareTrip() async {
    final tripDetails = '''
🚗 رحلة من ${widget.trip.fromCity} إلى ${widget.trip.toCity}
📅 التاريخ: ${widget.trip.departureDate.day}/${widget.trip.departureDate.month}/${widget.trip.departureDate.year}
⏰ الوقت: ${widget.trip.departureTime}
💰 السعر: ${widget.trip.price.toStringAsFixed(0)} درهم
🪑 المقاعد المتاحة: ${widget.trip.availableSeats}/${widget.trip.totalSeats}
${widget.trip.carModel != null ? '🚙 السيارة: ${widget.trip.carModel}' : ''}
${widget.trip.carPlate != null ? '🔢 اللوحة: ${widget.trip.carPlate}' : ''}

احجز الآن عبر تطبيق سفرني!
    ''';

    try {
      // Use clipboard for sharing (compatible with all platforms)
      await Clipboard.setData(ClipboardData(text: tripDetails));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ تفاصيل الرحلة إلى الحافظة'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في نسخ تفاصيل الرحلة'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
