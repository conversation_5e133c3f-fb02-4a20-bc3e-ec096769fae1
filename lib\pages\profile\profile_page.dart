import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/safe_network_image.dart';
import '../../services/storage_service.dart';
import '../../services/supabase_service.dart';

import '../../models/user_model.dart';
import 'driver_activation_page.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  UserModel? _userProfile;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get current authenticated user directly from Supabase
      final currentUser = Supabase.instance.client.auth.currentUser;

      if (currentUser == null) {
        setState(() {
          _error = 'لم يتم تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى';
          _isLoading = false;
        });
        return;
      }

      print('🔍 Loading profile for user ID: ${currentUser.id}');

      // Fetch user profile from users table using the authenticated user's ID
      final userProfile = await SupabaseService.getUserProfile(currentUser.id);

      if (!mounted) return;

      if (userProfile != null) {
        setState(() {
          _userProfile = userProfile;
          _isLoading = false;
        });

        // Update AuthProvider
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        authProvider.setCurrentUser(userProfile);

        print('✅ Profile loaded successfully: ${userProfile.fullName}');
      } else {
        setState(() {
          _error =
              'لم يتم العثور على بيانات المستخدم. قد تحتاج إلى إكمال إعداد الملف الشخصي';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;

      print('❌ Profile loading error: $e');
      setState(() {
        _error = 'حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUserProfile,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل البيانات...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error,
              ),
              const SizedBox(height: 16),
              Text(
                _error!,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.error,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadUserProfile,
                child: const Text('إعادة المحاولة'),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: _showDebugInfo,
                child: const Text('معلومات التشخيص'),
              ),
            ],
          ),
        ),
      );
    }

    if (_userProfile == null) {
      return const Center(
        child: Text('لم يتم العثور على بيانات المستخدم'),
      );
    }

    return _buildProfileContent(_userProfile!);
  }

  void _showDebugInfo() {
    final currentUser = Supabase.instance.client.auth.currentUser;
    final session = Supabase.instance.client.auth.currentSession;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التشخيص'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current User: ${currentUser?.id ?? 'null'}'),
            Text('Session User: ${session?.user.id ?? 'null'}'),
            Text('Session Valid: ${session != null}'),
            Text(
                'Auth Provider User: ${Provider.of<AuthProvider>(context, listen: false).currentUser?.id ?? 'null'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(UserModel user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Header
          _buildProfileHeader(user),

          const SizedBox(height: 24),

          // Personal Information
          _buildPersonalInfo(user),

          const SizedBox(height: 16),

          // Trip Leader Activation
          if (!user.isLeader) _buildTripLeaderActivation(),

          const SizedBox(height: 16),

          // Actions
          _buildActions(),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(UserModel user) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.secondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Profile Picture
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 40,
              backgroundColor: Colors.white,
              child: user.profileImageUrl != null
                  ? ClipOval(
                      child: SafeNetworkImage(
                        imageUrl: user.profileImageUrl!,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Icon(
                      Icons.person,
                      size: 40,
                      color: AppColors.primary,
                    ),
            ),
          ),

          const SizedBox(height: 16),

          // Name
          Text(
            user.fullName,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),

          const SizedBox(height: 8),

          // Phone
          Text(
            user.phone,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
          ),

          const SizedBox(height: 8),

          // Role Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              user.isLeader ? 'قائد رحلات' : 'مسافر',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Rating (if available)
          if (user.rating > 0) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 4),
                Text(
                  '${user.rating.toStringAsFixed(1)} (${user.totalRatings} تقييم)',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPersonalInfo(UserModel user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الشخصية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.phone, 'رقم الهاتف', user.phone),
            if (user.email != null)
              _buildInfoRow(Icons.email, 'البريد الإلكتروني', user.email!),
            _buildInfoRow(
              Icons.person,
              'نوع الحساب',
              user.isLeader ? 'قائد رحلات' : 'مسافر',
            ),
            if (user.city != null)
              _buildInfoRow(Icons.location_city, 'المدينة', user.city!),
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الانضمام',
              _formatDate(user.createdAt),
            ),
            if (user.isLeader) ...[
              _buildInfoRow(
                Icons.account_balance_wallet,
                'الرصيد',
                '${user.balance.toStringAsFixed(2)} درهم',
              ),
              _buildInfoRow(
                Icons.directions_car,
                'عدد الرحلات',
                '${user.totalTrips} رحلة',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.textSecondary),
          const SizedBox(width: 12),
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripLeaderActivation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.upgrade, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'تفعيل وضع قائد الرحلات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'قم بتفعيل وضع قائد الرحلات لتتمكن من إنشاء وإدارة الرحلات وكسب المال',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 16),
            CustomButton(
              text: 'تفعيل وضع قائد الرحلات',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DriverActivationPage(),
                  ),
                );
              },
              backgroundColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.edit, color: AppColors.primary),
            title: const Text('تعديل الملف الشخصي'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تعديل الملف الشخصي - قيد التطوير'),
                ),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.settings, color: AppColors.primary),
            title: const Text('الإعدادات'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('الإعدادات - قيد التطوير'),
                ),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
            onTap: () {
              _showLogoutDialog();
            },
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final authProvider =
                  Provider.of<AuthProvider>(context, listen: false);
              await authProvider.signOut();
              if (mounted) {
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/login',
                  (route) => false,
                );
              }
            },
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
